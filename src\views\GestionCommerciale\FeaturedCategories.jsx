import React, { useEffect, useState } from 'react';
import { fetchCategories, fetchFeaturedCategories, setFeaturedCategory, reorderFeaturedCategories } from '../../services/categoryService';
import { Table, Button, Form, Alert, Spinner, Container, Row, Col, Card, Badge, InputGroup } from 'react-bootstrap';
import { FaStar, FaRegStar, FaArrowUp, FaArrowDown, FaEdit, FaTrashAlt } from 'react-icons/fa';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';

export default function FeaturedCategories() {
  const [categories, setCategories] = useState([]);
  const [featuredCategories, setFeaturedCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [editingCategory, setEditingCategory] = useState(null);
  const [formData, setFormData] = useState({
    featured: true,
    featured_order: 0
  });

  // Load data
  const loadData = async () => {
    setLoading(true);
    setError('');
    try {
      const [allCategories, featured] = await Promise.all([fetchCategories(), fetchFeaturedCategories()]);
      setCategories(allCategories);
      setFeaturedCategories(featured);
    } catch (e) {
      setError(e.message);
    }
    setLoading(false);
  };

  useEffect(() => {
    loadData();
  }, []);

  // Handle form changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle featured status toggle
  const handleToggleFeatured = async (category, featured) => {
    try {
      setSubmitting(true);
      await setFeaturedCategory(category.id, {
        featured: featured,
        featured_order: featured
          ? featuredCategories.length > 0
            ? Math.max(...featuredCategories.map((c) => c.featured_order || 0)) + 1
            : 0
          : 0
      });
      setSuccess(`Catégorie ${featured ? 'ajoutée aux' : 'retirée des'} mises en avant`);
      loadData();
    } catch (e) {
      setError(e.message);
    } finally {
      setSubmitting(false);
      setTimeout(() => setSuccess(''), 3000);
    }
  };

  // Handle drag and drop reordering
  const handleDragEnd = async (result) => {
    if (!result.destination) return;

    const items = Array.from(featuredCategories);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update local state immediately for better UX
    setFeaturedCategories(items);

    // Prepare data for API
    const reorderData = {
      categories: items.map((item, index) => ({
        id: item.id,
        featured_order: index
      }))
    };

    try {
      setSubmitting(true);
      await reorderFeaturedCategories(reorderData);
      setSuccess('Ordre des catégories mis à jour');
    } catch (e) {
      setError(e.message);
      // Revert to previous state on error
      loadData();
    } finally {
      setSubmitting(false);
      setTimeout(() => setSuccess(''), 3000);
    }
  };

  // Move category up or down in order
  const moveCategory = async (index, direction) => {
    if ((direction === 'up' && index === 0) || (direction === 'down' && index === featuredCategories.length - 1)) {
      return;
    }

    const newIndex = direction === 'up' ? index - 1 : index + 1;
    const items = Array.from(featuredCategories);
    const [movedItem] = items.splice(index, 1);
    items.splice(newIndex, 0, movedItem);

    // Update local state immediately for better UX
    setFeaturedCategories(items);

    // Prepare data for API
    const reorderData = {
      categories: items.map((item, idx) => ({
        id: item.id,
        featured_order: idx
      }))
    };

    try {
      setSubmitting(true);
      await reorderFeaturedCategories(reorderData);
      setSuccess('Ordre des catégories mis à jour');
    } catch (e) {
      setError(e.message);
      // Revert to previous state on error
      loadData();
    } finally {
      setSubmitting(false);
      setTimeout(() => setSuccess(''), 3000);
    }
  };

  return (
    <Container fluid className="py-4">
      {/* Header */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaStar className="me-2" />
          Catégories Mises en Avant
        </h2>
        <p className="text-muted">Gérez les catégories qui apparaîtront en évidence sur la page d'accueil du site.</p>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}

      {/* Featured Categories List */}
      <Card className="border-0 shadow-sm mb-4">
        <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
          <h5 className="mb-0 fw-bold">Catégories mises en avant</h5>
          <div className="text-muted small">
            {featuredCategories.length} catégorie{featuredCategories.length !== 1 ? 's' : ''} mise
            {featuredCategories.length !== 1 ? 's' : ''} en avant
          </div>
        </Card.Header>
        <Card.Body className="p-0">
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3 text-muted">Chargement des catégories...</p>
            </div>
          ) : featuredCategories.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <FaRegStar style={{ fontSize: '3rem' }} className="text-muted" />
              </div>
              <p className="text-muted">Aucune catégorie mise en avant.</p>
              <p className="text-muted">Utilisez le tableau ci-dessous pour ajouter des catégories à mettre en avant.</p>
            </div>
          ) : (
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="featuredCategories">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    <Table hover responsive className="align-middle mb-0">
                      <thead>
                        <tr className="bg-light">
                          <th className="ps-3" style={{ width: '60px' }}>
                            Ordre
                          </th>
                          <th style={{ width: '60px' }}>ID</th>
                          <th style={{ width: '30%' }}>Nom</th>
                          <th style={{ width: '40%' }}>Description</th>
                          <th className="text-end pe-3" style={{ width: '20%' }}>
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {featuredCategories.map((category, index) => (
                          <Draggable key={category.id} draggableId={`category-${category.id}`} index={index}>
                            {(provided) => (
                              <tr
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                className="border-bottom"
                              >
                                <td className="ps-3 text-center">
                                  <Badge bg="primary" pill>
                                    {index + 1}
                                  </Badge>
                                </td>
                                <td>{category.id}</td>
                                <td>
                                  <div className="d-flex align-items-center">
                                    <div className="color-dot bg-warning me-2"></div>
                                    <span className="fw-medium">{category.nom || category.nom_categorie}</span>
                                  </div>
                                </td>
                                <td>
                                  <div className="text-truncate" style={{ maxWidth: '300px' }}>
                                    {category.description || category.description_categorie || (
                                      <span className="text-muted fst-italic">Aucune description</span>
                                    )}
                                  </div>
                                </td>
                                <td className="text-end pe-3">
                                  <Button
                                    size="sm"
                                    variant="outline-secondary"
                                    className="me-1"
                                    onClick={() => moveCategory(index, 'up')}
                                    disabled={index === 0 || submitting}
                                  >
                                    <FaArrowUp />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline-secondary"
                                    className="me-1"
                                    onClick={() => moveCategory(index, 'down')}
                                    disabled={index === featuredCategories.length - 1 || submitting}
                                  >
                                    <FaArrowDown />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline-danger"
                                    onClick={() => handleToggleFeatured(category, false)}
                                    disabled={submitting}
                                  >
                                    <FaTrashAlt className="me-1" /> Retirer
                                  </Button>
                                </td>
                              </tr>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </tbody>
                    </Table>
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          )}
        </Card.Body>
      </Card>

      {/* All Categories */}
      <Card className="border-0 shadow-sm">
        <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
          <h5 className="mb-0 fw-bold">Toutes les catégories</h5>
          <div className="text-muted small">
            {categories.length} catégorie{categories.length !== 1 ? 's' : ''} au total
          </div>
        </Card.Header>
        <Card.Body className="p-0">
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3 text-muted">Chargement des catégories...</p>
            </div>
          ) : categories.length === 0 ? (
            <div className="text-center py-5">
              <p className="text-muted">Aucune catégorie trouvée.</p>
            </div>
          ) : (
            <Table hover responsive className="align-middle mb-0">
              <thead>
                <tr className="bg-light">
                  <th className="ps-3" style={{ width: '60px' }}>
                    ID
                  </th>
                  <th style={{ width: '30%' }}>Nom</th>
                  <th style={{ width: '40%' }}>Description</th>
                  <th className="text-end pe-3" style={{ width: '20%' }}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {categories
                  .filter((cat) => !featuredCategories.some((fc) => fc.id === cat.id))
                  .map((category) => (
                    <tr key={category.id} className="border-bottom">
                      <td className="ps-3">{category.id}</td>
                      <td>
                        <div className="d-flex align-items-center">
                          <div className="color-dot bg-primary me-2"></div>
                          <span className="fw-medium">{category.nom || category.nom_categorie}</span>
                        </div>
                      </td>
                      <td>
                        <div className="text-truncate" style={{ maxWidth: '300px' }}>
                          {category.description || category.description_categorie || (
                            <span className="text-muted fst-italic">Aucune description</span>
                          )}
                        </div>
                      </td>
                      <td className="text-end pe-3">
                        <Button
                          size="sm"
                          variant="outline-warning"
                          onClick={() => handleToggleFeatured(category, true)}
                          disabled={submitting}
                        >
                          <FaStar className="me-1" /> Mettre en avant
                        </Button>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Custom CSS for styling */}
      <style jsx="true">{`
        .color-dot {
          width: 10px;
          height: 10px;
          border-radius: 50%;
        }
      `}</style>
    </Container>
  );
}
