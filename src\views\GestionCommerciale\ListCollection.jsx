import React, { useState, useEffect } from 'react';
import { Container, <PERSON>, <PERSON>, <PERSON><PERSON>, Al<PERSON>, <PERSON><PERSON>, Bad<PERSON>, Modal, <PERSON>, Row, Col, Breadcrumb, InputGroup } from 'react-bootstrap';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaEye, FaHome, FaLayerGroup, FaSearch, FaCalendarAlt } from 'react-icons/fa';
import {
  fetchCollections,
  createCollection,
  updateCollection,
  deleteCollection,
  fetchCollectionProducts
} from '../../services/collectionService';
import 'ui-component/extended/ProfessionalModal.css';
import TablePagination from 'components/TablePagination';

const ListCollection = () => {
  // States
  const [collections, setCollections] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedCollection, setSelectedCollection] = useState(null);
  const [collectionProducts, setCollectionProducts] = useState([]);
  const [modalLoading, setModalLoading] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    nom: '',
    description: '',
    image: '',
    active: true,
    date_debut: '',
    date_fin: ''
  });
  const [editingId, setEditingId] = useState(null);

  // Load collections on component mount
  useEffect(() => {
    loadCollections();
  }, []);

  const loadCollections = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await fetchCollections();
      console.log('Collections loaded:', response);

      // Handle different response formats
      const collectionsData = response.data || response;

      // Debug: Log the first collection to see its structure
      if (Array.isArray(collectionsData) && collectionsData.length > 0) {
        console.log('First collection structure:', collectionsData[0]);
        console.log('Available product count fields:', {
          products_count: collectionsData[0].products_count,
          produits_count: collectionsData[0].produits_count,
          produits_length: collectionsData[0].produits?.length,
          produits: collectionsData[0].produits
        });
      }

      const collections = Array.isArray(collectionsData) ? collectionsData : [];
      setCollections(collections);

      // If collections don't have product counts, fetch them in the background
      const needsProductCounts = collections.some((collection) => getProductCount(collection) === null);

      if (needsProductCounts) {
        console.log('Some collections missing product counts, fetching in background...');
        // Fetch product counts in background without blocking the UI
        setTimeout(async () => {
          try {
            const collectionsWithCounts = await Promise.all(
              collections.map(async (collection) => {
                const currentCount = getProductCount(collection);

                // If we don't have a product count, try to fetch it
                if (currentCount === null) {
                  try {
                    const products = await fetchCollectionProducts(collection.id);
                    const productCount = (products.data || products || []).length;
                    return { ...collection, products_count: productCount };
                  } catch (error) {
                    console.warn(`Failed to fetch product count for collection ${collection.id}:`, error);
                    return collection;
                  }
                }

                return collection;
              })
            );

            setCollections(collectionsWithCounts);
          } catch (error) {
            console.error('Error fetching product counts:', error);
          }
        }, 100);
      }
    } catch (err) {
      console.error('Error loading collections:', err);
      setError('Erreur lors du chargement des collections: ' + err.message);
      setCollections([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter collections based on search term
  const filteredCollections = collections.filter(
    (collection) =>
      collection.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      collection.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle form changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle form submit
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.nom.trim()) {
      setError('Le nom de la collection est obligatoire');
      return;
    }

    try {
      setModalLoading(true);
      setError('');

      if (editingId) {
        await updateCollection(editingId, formData);
        setSuccess('Collection mise à jour avec succès!');
      } else {
        await createCollection(formData);
        setSuccess('Collection créée avec succès!');
      }

      handleCloseModal();
      loadCollections();
    } catch (err) {
      console.error('Error saving collection:', err);
      setError('Erreur lors de la sauvegarde: ' + err.message);
    } finally {
      setModalLoading(false);
    }
  };

  // Handle edit
  const handleEdit = (collection) => {
    setFormData({
      nom: collection.nom || '',
      description: collection.description || '',
      image: collection.image || '',
      active: collection.active !== false,
      date_debut: collection.date_debut || '',
      date_fin: collection.date_fin || ''
    });
    setEditingId(collection.id);
    setShowModal(true);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!selectedCollection) return;

    try {
      setModalLoading(true);
      await deleteCollection(selectedCollection.id);
      setSuccess('Collection supprimée avec succès!');
      setShowDeleteModal(false);
      setSelectedCollection(null);
      loadCollections();
    } catch (err) {
      console.error('Error deleting collection:', err);
      setError('Erreur lors de la suppression: ' + err.message);
    } finally {
      setModalLoading(false);
    }
  };

  // Handle view details
  const handleViewDetails = async (collection) => {
    setSelectedCollection(collection);
    setShowDetailsModal(true);

    try {
      setModalLoading(true);
      const products = await fetchCollectionProducts(collection.id);
      setCollectionProducts(products.data || products || []);
    } catch (err) {
      console.error('Error loading collection products:', err);
      setCollectionProducts([]);
    } finally {
      setModalLoading(false);
    }
  };

  // Handle close modal
  const handleCloseModal = () => {
    setShowModal(false);
    setFormData({
      nom: '',
      description: '',
      image: '',
      active: true,
      date_debut: '',
      date_fin: ''
    });
    setEditingId(null);
    setError('');
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  // Get product count for a collection
  const getProductCount = (collection) => {
    // Try different possible fields for product count
    if (typeof collection.products_count === 'number') {
      return collection.products_count;
    }
    if (typeof collection.produits_count === 'number') {
      return collection.produits_count;
    }
    if (Array.isArray(collection.produits)) {
      return collection.produits.length;
    }
    if (Array.isArray(collection.products)) {
      return collection.products.length;
    }
    return null; // Return null instead of 0 to indicate unknown count
  };

  // Render product count badge
  const renderProductCount = (collection) => {
    const count = getProductCount(collection);

    if (count === null) {
      return (
        <Badge bg="secondary" className="rounded-pill">
          <Spinner size="sm" animation="border" style={{ width: '12px', height: '12px' }} className="me-1" />
          Chargement...
        </Badge>
      );
    }

    return (
      <Badge bg="info" className="rounded-pill">
        {count} produit(s)
      </Badge>
    );
  };

  // Pagination logic
  const totalPages = Math.ceil(filteredCollections.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentCollections = filteredCollections.slice(startIndex, endIndex);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handle pagination
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page
  };

  return (
    <Container fluid className="py-4">
      {/* Header and Breadcrumb */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaLayerGroup className="me-2" />
          Liste des Collections
        </h2>
        <Breadcrumb>
          <Breadcrumb.Item href="/dashboard">
            <FaHome size={14} className="me-1" /> Accueil
          </Breadcrumb.Item>
          <Breadcrumb.Item active>Collections</Breadcrumb.Item>
        </Breadcrumb>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <Alert variant="success" onClose={() => setSuccess('')} dismissible>
          {success}
        </Alert>
      )}
      {error && (
        <Alert variant="danger" onClose={() => setError('')} dismissible>
          {error}
        </Alert>
      )}

      {/* Search and Add Button */}
      <Card className="mb-4 shadow-sm border-0">
        <Card.Body>
          <Row className="align-items-center">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <FaSearch />
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Rechercher une collection..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
            <Col md={6} className="text-end">
              <Button variant="primary" onClick={() => setShowModal(true)} className="px-4">
                <FaPlus className="me-2" />
                Ajouter une Collection
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Collections Table */}
      <Card className="shadow-sm border-0">
        <Card.Body>
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-2 text-muted">Chargement des collections...</p>
            </div>
          ) : filteredCollections.length === 0 ? (
            <div className="text-center py-5">
              <FaLayerGroup size={48} className="text-muted mb-3" />
              <h5 className="text-muted">Aucune collection trouvée</h5>
              <p className="text-muted">
                {searchTerm ? 'Aucune collection ne correspond à votre recherche.' : 'Commencez par créer votre première collection.'}
              </p>
              {!searchTerm && (
                <Button variant="primary" onClick={() => setShowModal(true)} className="mt-2">
                  <FaPlus className="me-2" />
                  Créer une Collection
                </Button>
              )}
            </div>
          ) : (
            <div className="table-responsive">
              <Table hover className="align-middle">
                <thead className="table-light">
                  <tr>
                    <th>Nom</th>
                    <th>Description</th>
                    <th>Statut</th>
                    <th>Dates</th>
                    <th>Produits</th>
                    <th className="text-center">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {currentCollections.map((collection) => (
                    <tr key={collection.id}>
                      <td>
                        <div className="fw-medium">{collection.nom}</div>
                        {collection.image && <small className="text-muted">Image: {collection.image}</small>}
                      </td>
                      <td>
                        <div className="text-truncate" style={{ maxWidth: '200px' }}>
                          {collection.description || '-'}
                        </div>
                      </td>
                      <td>
                        <Badge bg={collection.active ? 'success' : 'secondary'}>{collection.active ? 'Active' : 'Inactive'}</Badge>
                      </td>
                      <td>
                        <div className="small">
                          <div>
                            <FaCalendarAlt className="me-1" />
                            Début: {formatDate(collection.date_debut)}
                          </div>
                          <div>
                            <FaCalendarAlt className="me-1" />
                            Fin: {formatDate(collection.date_fin)}
                          </div>
                        </div>
                      </td>
                      <td>{renderProductCount(collection)}</td>
                      <td className="text-center">
                        <div className="btn-group" role="group">
                          <Button size="sm" variant="outline-info" onClick={() => handleViewDetails(collection)} title="Voir les détails">
                            <FaEye />
                          </Button>
                          <Button size="sm" variant="outline-primary" onClick={() => handleEdit(collection)} title="Modifier">
                            <FaPencilAlt />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline-danger"
                            onClick={() => {
                              setSelectedCollection(collection);
                              setShowDeleteModal(true);
                            }}
                            title="Supprimer"
                          >
                            <FaTrashAlt />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>

              {/* Pagination */}
              <TablePagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={filteredCollections.length}
                itemsPerPage={itemsPerPage}
                startIndex={startIndex}
                endIndex={endIndex}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                showDirectPageInput={totalPages > 5}
              />
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Add/Edit Modal */}
      <Modal
        show={showModal}
        onHide={handleCloseModal}
        size="md"
        className="professional-modal"
        dialogClassName="professional-modal-dialog"
      >
        <Modal.Header closeButton className="professional-modal-header bg-primary text-white">
          <Modal.Title>{editingId ? 'Modifier la Collection' : 'Ajouter une Collection'}</Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body className="professional-modal-body">
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Nom de la collection *</Form.Label>
                  <Form.Control
                    type="text"
                    name="nom"
                    value={formData.nom}
                    onChange={handleChange}
                    required
                    placeholder="Nom de la collection"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Image</Form.Label>
                  <Form.Control type="text" name="image" value={formData.image} onChange={handleChange} placeholder="URL de l'image" />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="Description de la collection"
              />
            </Form.Group>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Date de début</Form.Label>
                  <Form.Control type="date" name="date_debut" value={formData.date_debut} onChange={handleChange} />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Date de fin</Form.Label>
                  <Form.Control type="date" name="date_fin" value={formData.date_fin} onChange={handleChange} />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Check type="checkbox" name="active" checked={formData.active} onChange={handleChange} label="Collection active" />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer className="professional-modal-footer">
            <Button variant="secondary" onClick={handleCloseModal}>
              Annuler
            </Button>
            <Button type="submit" variant="primary" disabled={modalLoading}>
              {modalLoading ? (
                <>
                  <Spinner size="sm" animation="border" className="me-2" />
                  Traitement...
                </>
              ) : editingId ? (
                'Mettre à jour'
              ) : (
                'Créer'
              )}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        show={showDeleteModal}
        onHide={() => setShowDeleteModal(false)}
        className="professional-modal"
        dialogClassName="professional-modal-dialog"
      >
        <Modal.Header closeButton className="professional-modal-header bg-danger text-white">
          <Modal.Title>Confirmer la suppression</Modal.Title>
        </Modal.Header>
        <Modal.Body className="professional-modal-body">
          <p>
            Êtes-vous sûr de vouloir supprimer la collection <strong>"{selectedCollection?.nom}"</strong> ?
          </p>
          <p className="text-muted small">Cette action est irréversible et supprimera également tous les liens avec les produits.</p>
        </Modal.Body>
        <Modal.Footer className="professional-modal-footer">
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Annuler
          </Button>
          <Button variant="danger" onClick={handleDelete} disabled={modalLoading}>
            {modalLoading ? (
              <>
                <Spinner size="sm" animation="border" className="me-2" />
                Suppression...
              </>
            ) : (
              'Supprimer'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Details Modal */}
      <Modal
        show={showDetailsModal}
        onHide={() => setShowDetailsModal(false)}
        size="lg"
        className="professional-modal"
        dialogClassName="professional-modal-dialog"
      >
        <Modal.Header closeButton className="professional-modal-header bg-info text-white">
          <Modal.Title>Détails de la collection: {selectedCollection?.nom}</Modal.Title>
        </Modal.Header>
        <Modal.Body className="professional-modal-body">
          {selectedCollection && (
            <Row>
              <Col md={6}>
                <Card className="h-100">
                  <Card.Header>
                    <h6 className="mb-0">Informations générales</h6>
                  </Card.Header>
                  <Card.Body>
                    <div className="mb-3">
                      <strong>Nom:</strong> {selectedCollection.nom}
                    </div>
                    <div className="mb-3">
                      <strong>Description:</strong> {selectedCollection.description || 'Aucune description'}
                    </div>
                    <div className="mb-3">
                      <strong>Statut:</strong>{' '}
                      <Badge bg={selectedCollection.active ? 'success' : 'secondary'}>
                        {selectedCollection.active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    <div className="mb-3">
                      <strong>Date de début:</strong> {formatDate(selectedCollection.date_debut)}
                    </div>
                    <div className="mb-3">
                      <strong>Date de fin:</strong> {formatDate(selectedCollection.date_fin)}
                    </div>
                    {selectedCollection.image && (
                      <div className="mb-3">
                        <strong>Image:</strong> {selectedCollection.image}
                      </div>
                    )}
                    <div className="mb-3">
                      <strong>Créée le:</strong> {formatDate(selectedCollection.created_at)}
                    </div>
                    <div>
                      <strong>Modifiée le:</strong> {formatDate(selectedCollection.updated_at)}
                    </div>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={6}>
                <Card className="h-100">
                  <Card.Header>
                    <h6 className="mb-0">Produits ({collectionProducts.length})</h6>
                  </Card.Header>
                  <Card.Body>
                    {modalLoading ? (
                      <div className="text-center py-3">
                        <Spinner animation="border" size="sm" />
                        <p className="mt-2 small text-muted">Chargement des produits...</p>
                      </div>
                    ) : collectionProducts.length === 0 ? (
                      <div className="text-center py-3 text-muted">
                        <FaLayerGroup size={32} className="mb-2" />
                        <p>Aucun produit dans cette collection</p>
                      </div>
                    ) : (
                      <div className="table-responsive">
                        <Table size="sm" hover>
                          <thead>
                            <tr>
                              <th>Produit</th>
                              <th>Prix</th>
                              <th>Ordre</th>
                              <th>Vedette</th>
                            </tr>
                          </thead>
                          <tbody>
                            {collectionProducts.map((product) => (
                              <tr key={product.id}>
                                <td>
                                  <div className="fw-medium">{product.nom_produit}</div>
                                  <small className="text-muted">ID: {product.id}</small>
                                </td>
                                <td>{product.prix_produit}€</td>
                                <td>
                                  <Badge bg="secondary" className="rounded-pill">
                                    {product.pivot?.ordre || '-'}
                                  </Badge>
                                </td>
                                <td>
                                  {product.pivot?.featured ? <Badge bg="warning">Vedette</Badge> : <span className="text-muted">-</span>}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </Table>
                      </div>
                    )}
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer className="professional-modal-footer">
          <Button variant="secondary" onClick={() => setShowDetailsModal(false)}>
            Fermer
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default ListCollection;
