// Service for fetching categories, sous-categories, and sous-sous-categories

const API_URL = import.meta.env.DEV ? '/api' : (import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api');

// Helper functions for polymorphic image management
export async function fetchModelImages(modelType, modelId) {
  try {
    const res = await fetch(`${API_URL}/images/get?model_type=${modelType}&model_id=${modelId}`);
    if (!res.ok) return [];
    const data = await res.json();
    return data.images || [];
  } catch (error) {
    console.error(`Error fetching images for ${modelType} ${modelId}:`, error);
    return [];
  }
}

export async function uploadImageForModel(modelType, modelId, imageFile, isPrimary = true) {
  try {
    const formData = new FormData();
    formData.append('model_type', modelType);
    formData.append('model_id', modelId);
    formData.append('image', imageFile);
    formData.append('is_primary', isPrimary.toString());

    const res = await fetch(`${API_URL}/images/upload`, {
      method: 'POST',
      body: formData
    });

    if (!res.ok) throw new Error('Failed to upload image');
    return await res.json();
  } catch (error) {
    console.error(`Error uploading image for ${modelType} ${modelId}:`, error);
    throw error;
  }
}

export async function deleteImage(imageId) {
  try {
    const res = await fetch(`${API_URL}/images/${imageId}`, {
      method: 'DELETE'
    });
    if (!res.ok) throw new Error('Failed to delete image');
    return await res.json();
  } catch (error) {
    console.error(`Error deleting image ${imageId}:`, error);
    throw error;
  }
}

export async function fetchCategories() {
  const res = await fetch(`${API_URL}/categories`);
  if (!res.ok) throw new Error('Erreur lors du chargement des catégories');
  return res.json();
}

export async function fetchSousCategories(categorie_id) {
  const res = await fetch(`${API_URL}/sousCategories?categorie_id=${categorie_id}`);
  if (!res.ok) throw new Error('Erreur lors du chargement des sous-catégories');
  return res.json();
}

export async function fetchSousSousCategories(sous_categorie_id) {
  const res = await fetch(`${API_URL}/sous_sousCategories?sous_categorie_id=${sous_categorie_id}`);
  if (!res.ok) throw new Error('Erreur lors du chargement des sous-sous-catégories');
  return res.json();
}

// Category CRUD
export async function fetchCategoryById(id) {
  const res = await fetch(`${API_URL}/categories/${id}`);
  if (!res.ok) throw new Error('Erreur lors du chargement de la catégorie');
  return res.json();
}

export async function createCategory(data) {
  // Map field names to match API expectations
  const apiData = {
    nom_categorie: data.nom,
    description_categorie: data.description,
    image_categorie: data.image
  };

  console.log('📝 Creating category with mapped data:', apiData);

  const res = await fetch(`${API_URL}/categories`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error('Erreur lors de la création de la catégorie');
  return res.json();
}

export async function updateCategory(id, data) {
  try {
    console.log(`🔄 Attempting to update category with ID: ${id}`);
    console.log(`📝 Original data received:`, data);

    // Map field names to match API expectations
    const apiData = {
      nom_categorie: data.nom,
      description_categorie: data.description,
      image_categorie: data.image
    };

    console.log(`📝 Mapped data to send:`, apiData);
    const url = `${API_URL}/categories/${id}`;
    console.log(`🌐 PUT request to: ${url}`);

    const res = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(apiData)
    });

    console.log(`📡 Response status: ${res.status}`);

    if (!res.ok) {
      let errorData;
      try {
        errorData = await res.json();
      } catch (parseError) {
        console.error('Failed to parse error response:', parseError);
        throw new Error(`Erreur HTTP ${res.status}: ${res.statusText}`);
      }
      console.error('❌ Update failed with error:', errorData);
      throw new Error(errorData.message || `Erreur lors de la mise à jour de la catégorie (${res.status})`);
    }

    const result = await res.json();
    console.log('📋 Update response:', result);

    // Check if the response contains an error even with 200 status
    if (result.error) {
      console.error('❌ Server returned error:', result.error);
      throw new Error(result.error || 'Erreur lors de la mise à jour de la catégorie');
    }

    console.log('✅ Category updated successfully');
    return result;
  } catch (error) {
    console.error('❌ Error updating category:', error);
    throw error;
  }
}

export async function deleteCategory(id) {
  try {
    console.log(`🗑️ Attempting to delete category with ID: ${id}`);
    const url = `${API_URL}/categories/${id}`;
    console.log(`🌐 DELETE request to: ${url}`);

    const res = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    console.log(`📡 Response status: ${res.status}`);

    if (!res.ok) {
      let errorData;
      try {
        errorData = await res.json();
      } catch (parseError) {
        console.error('Failed to parse error response:', parseError);
        throw new Error(`Erreur HTTP ${res.status}: ${res.statusText}`);
      }
      console.error('❌ Delete failed with error:', errorData);
      throw new Error(errorData.message || `Erreur lors de la suppression de la catégorie (${res.status})`);
    }

    const result = await res.json();
    console.log('📋 Delete response:', result);

    // Check if the response contains an error even with 200 status
    if (result.error) {
      console.error('❌ Server returned error:', result.error);

      // Handle foreign key constraint violation
      if (result.error.includes('Foreign key violation') ||
          result.error.includes('SQLSTATE[23503]') ||
          result.error.includes('foreign key constraint') ||
          result.error.includes('sous-catégories')) {
        throw new Error('Impossible de supprimer cette catégorie car elle contient encore des sous-catégories ou des produits. Veuillez d\'abord supprimer tous les éléments liés à cette catégorie.');
      }

      throw new Error(result.error || 'Erreur lors de la suppression de la catégorie');
    }

    console.log('✅ Category deleted successfully:', result);
    return result;
  } catch (error) {
    console.error('❌ Error deleting category:', error);
    throw error;
  }
}

// Sous-catégorie CRUD
export async function fetchAllSousCategories() {
  const res = await fetch(`${API_URL}/sousCategories`);
  if (!res.ok) throw new Error('Erreur lors du chargement des sous-catégories');
  return res.json();
}

export async function fetchSousCategorieById(id) {
  const res = await fetch(`${API_URL}/sousCategories/${id}`);
  if (!res.ok) throw new Error('Erreur lors du chargement de la sous-catégorie');
  return res.json();
}

export async function createSousCategorie(data) {
  // Create sous-category without image (images handled separately via polymorphic API)
  const apiData = {
    nom_sous_categorie: data.nom,
    description_sous_categorie: data.description,
    categorie_id: data.categorie_id
  };

  console.log('📝 Creating sous-category with mapped data:', apiData);

  const res = await fetch(`${API_URL}/sousCategories`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error('Erreur lors de la création de la sous-catégorie');

  const result = await res.json();

  // Handle image upload if provided
  if (data.imageFile && result.id) {
    try {
      await uploadImageForModel('sous_categorie', result.id, data.imageFile, true);
      console.log('✅ Image uploaded for sous-category:', result.id);
    } catch (imageError) {
      console.warn('⚠️ Image upload failed but category was created:', imageError);
    }
  }

  return result;
}

export async function updateSousCategorie(id, data) {
  try {
    console.log(`🔄 Attempting to update sous-categorie with ID: ${id}`);
    console.log(`📝 Original data received:`, data);

    // Update sous-category without image (images handled separately via polymorphic API)
    const apiData = {
      nom_sous_categorie: data.nom,
      description_sous_categorie: data.description,
      categorie_id: data.categorie_id
    };

    console.log(`📝 Mapped data to send:`, apiData);
    const url = `${API_URL}/sousCategories/${id}`;
    console.log(`🌐 PUT request to: ${url}`);

    const res = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(apiData)
    });

    if (!res.ok) {
      const errorText = await res.text();
      console.error('❌ Update failed with response:', errorText);
      throw new Error(`Erreur lors de la mise à jour de la sous-catégorie: ${res.status}`);
    }

    const result = await res.json();
    console.log('✅ Sous-category updated successfully:', result);

    // Handle image upload if provided
    if (data.imageFile) {
      try {
        await uploadImageForModel('sous_categorie', id, data.imageFile, true);
        console.log('✅ Image updated for sous-category:', id);
      } catch (imageError) {
        console.warn('⚠️ Image update failed but category was updated:', imageError);
      }
    }

    return result;
  } catch (error) {
    console.error('❌ Error updating sous-categorie:', error);
    throw error;
  }
}

export async function deleteSousCategorie(id) {
  try {
    console.log(`🗑️ Attempting to delete sous-categorie with ID: ${id}`);
    const url = `${API_URL}/sousCategories/${id}`;
    console.log(`🌐 DELETE request to: ${url}`);

    const res = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    console.log(`📡 Response status: ${res.status}`);

    if (!res.ok) {
      let errorData;
      try {
        errorData = await res.json();
      } catch (parseError) {
        console.error('Failed to parse error response:', parseError);
        throw new Error(`Erreur HTTP ${res.status}: ${res.statusText}`);
      }
      console.error('❌ Delete failed with error:', errorData);
      throw new Error(errorData.message || `Erreur lors de la suppression de la sous-catégorie (${res.status})`);
    }

    const result = await res.json();
    console.log('📋 Delete response:', result);

    // Check if the response contains an error even with 200 status
    if (result.error) {
      console.error('❌ Server returned error:', result.error);

      // Handle foreign key constraint violation
      if (result.error.includes('Foreign key violation') ||
          result.error.includes('SQLSTATE[23503]') ||
          result.error.includes('foreign key constraint') ||
          result.error.includes('sous-sous-catégories') ||
          result.error.includes('produits')) {
        throw new Error('Impossible de supprimer cette sous-catégorie car elle contient encore des sous-sous-catégories ou des produits. Veuillez d\'abord supprimer tous les éléments liés à cette sous-catégorie.');
      }

      throw new Error(result.error || 'Erreur lors de la suppression de la sous-catégorie');
    }

    console.log('✅ Sous-categorie deleted successfully:', result);
    return result;
  } catch (error) {
    console.error('❌ Error deleting sous-categorie:', error);
    throw error;
  }
}

export async function fetchSousCategoriesByCategoryId(categorie_id) {
  const res = await fetch(`${API_URL}/categories/${categorie_id}/sousCategories`);
  if (!res.ok) throw new Error('Erreur lors du chargement des sous-catégories de la catégorie');
  return res.json();
}

// Sous-sous-catégorie CRUD
export async function fetchAllSousSousCategories() {
  const res = await fetch(`${API_URL}/sous_sousCategories`);
  if (!res.ok) throw new Error('Erreur lors du chargement des sous-sous-catégories');
  return res.json();
}

export async function fetchSousSousCategorieById(id) {
  const res = await fetch(`${API_URL}/sous_sousCategories/${id}`);
  if (!res.ok) throw new Error('Erreur lors du chargement de la sous-sous-catégorie');
  return res.json();
}

export async function createSousSousCategorie(data) {
  // Create sous-sous-category without image (images handled separately via polymorphic API)
  const apiData = {
    nom_sous_sous_categorie: data.nom,
    description_sous_sous_categorie: data.description,
    sous_categorie_id: data.sous_categorie_id
  };

  console.log('📝 Creating sous-sous-category with mapped data:', apiData);

  const res = await fetch(`${API_URL}/sous_sousCategories`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error('Erreur lors de la création de la sous-sous-catégorie');

  const result = await res.json();

  // Handle image upload if provided
  if (data.imageFile && result.id) {
    try {
      await uploadImageForModel('sous_sous_categorie', result.id, data.imageFile, true);
      console.log('✅ Image uploaded for sous-sous-category:', result.id);
    } catch (imageError) {
      console.warn('⚠️ Image upload failed but category was created:', imageError);
    }
  }

  return result;
}

export async function updateSousSousCategorie(id, data) {
  try {
    console.log(`🔄 Attempting to update sous-sous-categorie with ID: ${id}`);
    console.log(`📝 Original data received:`, data);

    // Update sous-sous-category without image (images handled separately via polymorphic API)
    const apiData = {
      nom_sous_sous_categorie: data.nom,
      description_sous_sous_categorie: data.description,
      sous_categorie_id: data.sous_categorie_id
    };

    console.log(`📝 Mapped data to send:`, apiData);
    const url = `${API_URL}/sous_sousCategories/${id}`;
    console.log(`🌐 PUT request to: ${url}`);

    const res = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(apiData)
    });

    if (!res.ok) {
      const errorText = await res.text();
      console.error('❌ Update failed with response:', errorText);
      throw new Error(`Erreur lors de la mise à jour de la sous-sous-catégorie: ${res.status}`);
    }

    const result = await res.json();
    console.log('✅ Sous-sous-category updated successfully:', result);

    // Handle image upload if provided
    if (data.imageFile) {
      try {
        await uploadImageForModel('sous_sous_categorie', id, data.imageFile, true);
        console.log('✅ Image updated for sous-sous-category:', id);
      } catch (imageError) {
        console.warn('⚠️ Image update failed but category was updated:', imageError);
      }
    }

    return result;
  } catch (error) {
    console.error('❌ Error updating sous-sous-categorie:', error);
    throw error;
  }
}

export async function deleteSousSousCategorie(id) {
  try {
    console.log(`🗑️ Attempting to delete sous-sous-categorie with ID: ${id}`);
    const url = `${API_URL}/sous_sousCategories/${id}`;
    console.log(`🌐 DELETE request to: ${url}`);

    const res = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    console.log(`📡 Response status: ${res.status}`);

    if (!res.ok) {
      let errorData;
      try {
        errorData = await res.json();
      } catch (parseError) {
        console.error('Failed to parse error response:', parseError);
        throw new Error(`Erreur HTTP ${res.status}: ${res.statusText}`);
      }
      console.error('❌ Delete failed with error:', errorData);
      throw new Error(errorData.message || `Erreur lors de la suppression de la sous-sous-catégorie (${res.status})`);
    }

    const result = await res.json();
    console.log('📋 Delete response:', result);

    // Check if the response contains an error even with 200 status
    if (result.error) {
      console.error('❌ Server returned error:', result.error);

      // Handle foreign key constraint violation
      if (result.error.includes('Foreign key violation') ||
          result.error.includes('SQLSTATE[23503]') ||
          result.error.includes('foreign key constraint') ||
          result.error.includes('produits')) {
        throw new Error('Impossible de supprimer cette sous-sous-catégorie car elle contient encore des produits. Veuillez d\'abord supprimer tous les produits liés à cette sous-sous-catégorie.');
      }

      throw new Error(result.error || 'Erreur lors de la suppression de la sous-sous-catégorie');
    }

    console.log('✅ Sous-sous-categorie deleted successfully:', result);
    return result;
  } catch (error) {
    console.error('❌ Error deleting sous-sous-categorie:', error);
    throw error;
  }
}

// Featured Categories
export async function fetchFeaturedCategories() {
  const res = await fetch(`${API_URL}/categories/featured`);
  if (!res.ok) throw new Error('Erreur lors du chargement des catégories mises en avant');
  return res.json();
}

export async function setFeaturedCategory(id, data) {
  const res = await fetch(`${API_URL}/categories/${id}/featured`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Erreur lors de la mise à jour du statut de mise en avant');
  return res.json();
}

export async function reorderFeaturedCategories(data) {
  const res = await fetch(`${API_URL}/categories/featured/reorder`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Erreur lors de la réorganisation des catégories mises en avant');
  return res.json();
}
